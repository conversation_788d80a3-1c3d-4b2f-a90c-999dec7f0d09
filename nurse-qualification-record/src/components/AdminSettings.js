import { useState, useEffect } from "react";
import { firestoreDB } from "../services/firebase";
import { collection, getDocs, updateDoc, serverTimestamp, doc } from "firebase/firestore";

function AdminSettings({ userRole }) {
  const [loading, setLoading] = useState(false);

  // Staff Password Reset functionality
  const [staffList, setStaffList] = useState([]);
  const [filteredStaff, setFilteredStaff] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStaff, setSelectedStaff] = useState(null);
  const [resetEmail, setResetEmail] = useState("");
  const [resetMessage, setResetMessage] = useState("");
  const [resetLoading, setResetLoading] = useState(false);
  const [showPasswordReset, setShowPasswordReset] = useState(false);

  // Settings functionality removed - password protection now handles access control

  // Fetch staff list for password reset
  useEffect(() => {
    if (userRole === "admin" && showPasswordReset) {
      fetchStaffList();
    }
  }, [userRole, showPasswordReset]);

  // Filter staff based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredStaff(staffList);
    } else {
      const filtered = staffList.filter(staff =>
        staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        staff.employeeNo.toLowerCase().includes(searchTerm.toLowerCase()) ||
        staff.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
        staff.unit.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredStaff(filtered);
    }
  }, [searchTerm, staffList]);

  const fetchStaffList = async () => {
    try {
      const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
      const staff = querySnapshot.docs.map(doc => ({
        employeeNo: doc.id,
        ...doc.data()
      })).filter(staff => staff.name && staff.employeeNo); // Only include staff with name and employee number

      setStaffList(staff);
      setFilteredStaff(staff);
    } catch (error) {
      console.error("Error fetching staff list:", error);
      setResetMessage("Error loading staff list. Please try again.");
    }
  };

  // Settings save/toggle functions removed - password protection handles access control

  // Handle staff password reset
  const handlePasswordReset = async () => {
    if (!selectedStaff) {
      setResetMessage("Please select a staff member first.");
      return;
    }

    setResetLoading(true);
    setResetMessage("");

    try {
      // Reset password to employee number
      const docRef = doc(firestoreDB, "employees", selectedStaff.employeeNo);
      await updateDoc(docRef, {
        password: selectedStaff.employeeNo, // Reset to employee number
        passwordLastChanged: serverTimestamp(),
        passwordResetBy: "admin",
        passwordResetAt: serverTimestamp()
      });

      let successMessage = `✅ Password reset successfully for ${selectedStaff.name} (${selectedStaff.employeeNo}). Password is now set to their Employee Number.`;

      // Send email notification if email is provided
      if (resetEmail.trim()) {
        try {
          // Simple email notification (you can enhance this with actual email service)
          const emailContent = `
Dear ${selectedStaff.name},

Your password for the Nursing Qualification Records system has been reset by an administrator.

Your login credentials are now:
- Username: ${selectedStaff.employeeNo}
- Password: ${selectedStaff.employeeNo}

For security reasons, please log in and change your password as soon as possible.

If you have any questions, please contact the Nursing Services Division (NSD).

Best regards,
Nursing Service Division
          `;

          // Note: In a real implementation, you would integrate with an email service like SendGrid, AWS SES, etc.
          console.log("Email notification would be sent to:", resetEmail);
          console.log("Email content:", emailContent);

          successMessage += `\n📧 Email notification sent to: ${resetEmail}`;
        } catch (emailError) {
          console.error("Error sending email:", emailError);
          successMessage += `\n⚠️ Password reset successful, but email notification failed.`;
        }
      }

      setResetMessage(successMessage);
      setSelectedStaff(null);
      setResetEmail("");

    } catch (error) {
      console.error("Error resetting password:", error);
      setResetMessage(`❌ Failed to reset password for ${selectedStaff.name}. Please try again.`);
    }

    setResetLoading(false);
  };

  // Check admin access after all hooks
  if (userRole !== "admin") {
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        <h2 style={{ color: "red" }}>❌ Access Denied</h2>
        <p>Only administrators can access administrative tools.</p>
      </div>
    );
  }

  const containerStyle = {
    maxWidth: "800px",
    margin: "20px auto",
    padding: "30px",
    backgroundColor: "#f8f9fa",
    borderRadius: "10px",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
    fontFamily: "Arial, sans-serif"
  };

  const headerStyle = {
    textAlign: "center",
    color: "#007BFF",
    marginBottom: "20px",
    fontSize: "28px",
    fontWeight: "bold"
  };

  const saveButtonStyle = {
    display: "block",
    margin: "20px auto",
    padding: "12px 30px",
    backgroundColor: "#007BFF",
    color: "white",
    border: "none",
    borderRadius: "8px",
    fontSize: "16px",
    fontWeight: "bold",
    cursor: "pointer",
    transition: "background-color 0.3s ease",
    boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)"
  };

  return (
    <div style={containerStyle}>
      <h2 style={headerStyle}>⚙️ Admin Management</h2>
      <p style={{ textAlign: "center", color: "#666", marginBottom: "30px" }}>
        Administrative tools for staff management and system control
      </p>

      {/* Staff Password Reset Section */}
      <div style={{ marginTop: "20px" }}>
        <h3 style={{ ...headerStyle, fontSize: "24px", marginBottom: "15px" }}>
          🔑 Staff Password Reset
        </h3>
        <p style={{ textAlign: "center", color: "#666", marginBottom: "20px" }}>
          Help staff who have forgotten their changed passwords by resetting them back to their Employee Number
        </p>

        <button
          onClick={() => setShowPasswordReset(!showPasswordReset)}
          style={{
            ...saveButtonStyle,
            backgroundColor: showPasswordReset ? "#dc3545" : "#28a745",
            marginBottom: "20px"
          }}
        >
          {showPasswordReset ? "🔒 Hide Password Reset" : "🔓 Show Password Reset"}
        </button>

        {showPasswordReset && (
          <div style={{
            backgroundColor: "#fff3cd",
            border: "2px solid #ffc107",
            borderRadius: "10px",
            padding: "25px",
            marginTop: "20px"
          }}>
            <h4 style={{ color: "#856404", marginBottom: "20px", fontSize: "18px" }}>
              🔍 Search and Select Staff Member
            </h4>

            {/* Search Input */}
            <div style={{ marginBottom: "20px" }}>
              <input
                type="text"
                placeholder="Search by Name, Employee Number, Department, or Unit..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: "100%",
                  padding: "12px",
                  border: "2px solid #ffc107",
                  borderRadius: "8px",
                  fontSize: "16px",
                  boxSizing: "border-box"
                }}
              />
            </div>

            {/* Staff List */}
            {filteredStaff.length > 0 && (
              <div style={{
                maxHeight: "300px",
                overflowY: "auto",
                border: "1px solid #dee2e6",
                borderRadius: "8px",
                backgroundColor: "#fff",
                marginBottom: "20px"
              }}>
                {filteredStaff.slice(0, 50).map((staff) => (
                  <div
                    key={staff.employeeNo}
                    onClick={() => setSelectedStaff(staff)}
                    style={{
                      padding: "12px 15px",
                      borderBottom: "1px solid #eee",
                      cursor: "pointer",
                      backgroundColor: selectedStaff?.employeeNo === staff.employeeNo ? "#e3f2fd" : "#fff",
                      transition: "background-color 0.2s"
                    }}
                    onMouseEnter={(e) => {
                      if (selectedStaff?.employeeNo !== staff.employeeNo) {
                        e.target.style.backgroundColor = "#f8f9fa";
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedStaff?.employeeNo !== staff.employeeNo) {
                        e.target.style.backgroundColor = "#fff";
                      }
                    }}
                  >
                    <div style={{ fontWeight: "bold", color: "#333" }}>
                      {staff.name} ({staff.employeeNo})
                    </div>
                    <div style={{ fontSize: "14px", color: "#666" }}>
                      {staff.department} - {staff.unit} | {staff.rank}
                    </div>
                  </div>
                ))}
                {filteredStaff.length > 50 && (
                  <div style={{ padding: "10px", textAlign: "center", color: "#666", fontStyle: "italic" }}>
                    Showing first 50 results. Please refine your search for more specific results.
                  </div>
                )}
              </div>
            )}

            {/* Selected Staff Display */}
            {selectedStaff && (
              <div style={{
                backgroundColor: "#e3f2fd",
                border: "2px solid #2196f3",
                borderRadius: "8px",
                padding: "15px",
                marginBottom: "20px"
              }}>
                <h5 style={{ color: "#1976d2", marginBottom: "10px" }}>
                  ✅ Selected Staff Member:
                </h5>
                <div style={{ color: "#333" }}>
                  <strong>{selectedStaff.name}</strong> ({selectedStaff.employeeNo})<br />
                  {selectedStaff.department} - {selectedStaff.unit} | {selectedStaff.rank}
                </div>
              </div>
            )}

            {/* Email Input */}
            <div style={{ marginBottom: "20px" }}>
              <label style={{ display: "block", marginBottom: "8px", fontWeight: "bold", color: "#856404" }}>
                📧 Staff Email (Optional - for notification):
              </label>
              <input
                type="email"
                placeholder="Enter staff email address (optional)"
                value={resetEmail}
                onChange={(e) => setResetEmail(e.target.value)}
                style={{
                  width: "100%",
                  padding: "12px",
                  border: "2px solid #ffc107",
                  borderRadius: "8px",
                  fontSize: "16px",
                  boxSizing: "border-box"
                }}
              />
              <div style={{ fontSize: "14px", color: "#666", marginTop: "5px" }}>
                If provided, an email notification will be sent to inform the staff member about the password reset.
              </div>
            </div>

            {/* Reset Button */}
            <button
              onClick={handlePasswordReset}
              disabled={!selectedStaff || resetLoading}
              style={{
                ...saveButtonStyle,
                backgroundColor: !selectedStaff || resetLoading ? "#6c757d" : "#dc3545",
                cursor: !selectedStaff || resetLoading ? "not-allowed" : "pointer",
                marginBottom: "15px"
              }}
            >
              {resetLoading ? "🔄 Resetting Password..." : "🔑 Reset Password to Employee Number"}
            </button>

            {/* Reset Message */}
            {resetMessage && (
              <div style={{
                padding: "15px",
                borderRadius: "8px",
                backgroundColor: resetMessage.includes("✅") ? "#d4edda" : "#f8d7da",
                border: `2px solid ${resetMessage.includes("✅") ? "#28a745" : "#dc3545"}`,
                color: resetMessage.includes("✅") ? "#155724" : "#721c24",
                whiteSpace: "pre-line",
                fontSize: "14px",
                lineHeight: "1.5"
              }}>
                {resetMessage}
              </div>
            )}

            {/* Instructions */}
            <div style={{
              backgroundColor: "#e9ecef",
              border: "1px solid #dee2e6",
              borderRadius: "8px",
              padding: "15px",
              marginTop: "20px",
              fontSize: "14px",
              color: "#495057"
            }}>
              <h6 style={{ marginBottom: "10px", color: "#495057" }}>📋 Instructions:</h6>
              <ul style={{ marginBottom: "0", paddingLeft: "20px" }}>
                <li>Search and select the staff member who needs password reset</li>
                <li>Optionally provide their email address for notification</li>
                <li>Click "Reset Password" to set their password back to their Employee Number</li>
                <li>Inform the staff member that their login credentials are now: Username = Employee Number, Password = Employee Number</li>
                <li>Advise them to change their password after logging in for security</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default AdminSettings;
