import { useState, useEffect } from "react";
import * as XLSX from "xlsx";
import { firestoreDB } from "../services/firebase";
import { doc, setDoc, getDoc, serverTimestamp, collection, getDocs, updateDoc } from "firebase/firestore";

function ImportData({ userRole }) {
  const [message, setMessage] = useState("");
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [importStats, setImportStats] = useState({
    total: 0,
    processed: 0,
    newRecords: 0,
    updatedRecords: 0,
    duplicatesSkipped: 0,
    errors: 0
  });

  // User creation states
  const [showUserCreation, setShowUserCreation] = useState(false);
  const [departments, setDepartments] = useState([]);
  const [units, setUnits] = useState([]);
  const [userProfiles, setUserProfiles] = useState([]);
  const [creatingUsers, setCreatingUsers] = useState(false);
  const [userCreationMessage, setUserCreationMessage] = useState("");

  // Staff relocation states
  const [showStaffRelocation, setShowStaffRelocation] = useState(false);
  const [selectedDepartmentForFilter, setSelectedDepartmentForFilter] = useState("");
  const [selectedUnitForFilter, setSelectedUnitForFilter] = useState("");
  const [staffList, setStaffList] = useState([]);
  const [selectedStaff, setSelectedStaff] = useState("");
  const [newDepartment, setNewDepartment] = useState("");
  const [newUnit, setNewUnit] = useState("");
  const [relocatingStaff, setRelocatingStaff] = useState(false);
  const [relocationMessage, setRelocationMessage] = useState("");

  // Use importStats to avoid unused variable warning
  console.log("Import stats:", importStats);

  // Load departments when component mounts
  useEffect(() => {
    fetchDepartmentsAndUnits();
  }, []);

  const handleExcelImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Initial confirmation alert
    const confirmImport = window.confirm(
      `📁 File Selected: ${file.name}\n\n` +
      `Are you sure you want to import this Excel file?\n\n` +
      `⚠️ This will:\n` +
      `• Add new employee records\n` +
      `• Update existing employee information\n` +
      `• Add new qualifications to existing employees\n` +
      `• Skip duplicate qualifications\n\n` +
      `Click OK to proceed or Cancel to abort.`
    );

    if (!confirmImport) {
      // Reset file input
      event.target.value = '';
      return;
    }

    // Show processing start alert
    alert(
      `🚀 Starting Import Process...\n\n` +
      `Please wait while we process your file.\n` +
      `Do not close this window during the import.`
    );

    setUploading(true);
    setImportStats({
      total: 0,
      processed: 0,
      newRecords: 0,
      updatedRecords: 0,
      duplicatesSkipped: 0,
      errors: 0
    });
    const reader = new FileReader();

    reader.onerror = () => {
      alert(
        `❌ File Reading Error!\n\n` +
        `There was an error reading the Excel file.\n` +
        `Please check the file and try again.`
      );
      setUploading(false);
      event.target.value = '';
    };

    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        if (!jsonData || jsonData.length === 0) {
          alert(
            `⚠️ Empty File!\n\n` +
            `The Excel file appears to be empty or has no data rows.\n` +
            `Please check your file and try again.`
          );
          setUploading(false);
          event.target.value = '';
          return;
        }

      const total = jsonData.length;
      let stats = {
        total: total,
        processed: 0,
        newRecords: 0,
        updatedRecords: 0,
        duplicatesSkipped: 0,
        errors: 0
      };

      // Show file analysis alert
      alert(
        `📊 File Analysis Complete!\n\n` +
        `Total rows found: ${total}\n` +
        `Starting data processing...`
      );

      const mapDepartmentAndUnit = (row) => {
        let unit = row.Unit || "";
        let department = row.Department || "";

        const unitMappings = {
          "Integrated Disinfection & Sterilization Service at 2/F": "Integrated Disinfection & Sterilization Service",
          "Integrated Disinfection & Sterilization Service at LG": "Integrated Disinfection & Sterilization Service",
          "Integrated Surgical Centre": "ASS",
          "Day Surgery Operating Theatre": "ASS",
          "Day Surgery Day Area": "ASS",
          "Department of Accident and Emergency": "A&E/EMW",
          "Accident & Emergency Office": "A&E/EMW",
          "Emergency Medicine Ward": "A&E/EMW",
          "Combined Endoscopy Unit": "Combined Endoscopy Unit",
          "Combined Endoscopy Unit Day Service": "Combined Endoscopy Unit",
          "Ophthalmology Clinic": "EYE",
          "Cataract Centre": "EYE",
          "3B Rapid Assessment Ward": "3B Ward",
          "3B rTPA Stroke Service": "3B Ward",
          "High Dependency Unit": "ICU/HDU",
          "Intensive Care Unit": "ICU/HDU",
          "Clinical Preceptor & Executive Partnership": "NSD Office",
          "Central Relieving Pool": "NSD Office",
          "Nursing Services Division Office": "NSD Office",
          "Breast Care Nurse Clinic": "Surgery Office",
          "Nurse Clinic (Colon)": "Surgery Office",
          "Periopertive Surgical Nurse Clinic": "Surgery Office",
          "Surgery Office": "Surgery Office",
          "Urology Nurse Clinic": "Surgery Office"
        };

        const departmentMappings = {
          "Cataract Centre": "ent_dental",
          "Family Medicine Clinic (ACB)": "general_outpatient_clinics",
          "Mona Fong GOPC": "general_outpatient_clinics",
          "Po Ning Road GOPC": "general_outpatient_clinics",
          "Tseung Kwan O Jockey Club GOPC": "general_outpatient_clinics",
          "KEC Smoking Counselling & Cessation Centre": "general_outpatient_clinics",
          "3B Rapid Assessment Ward": "medical_special_services",
          "3B rTPA Stroke Service": "medical_special_services",
          "A&OT": "anaesthetic_theatre",
          "AED": "accident_emergency",
          "AMB": "ambulatory_services",
          "ENT": "ent_dental",
          "EYE": "ent_dental",
          "MED": "medical",
          "NSD": "nursing_service_division",
          "O&T": "orthopaedics_gynaecology",
          "O&T/G": "orthopaedics_gynaecology",
          "P&AM": "paediatrics",
          "GOPC": "general_outpatient_clinics",
          "SURG": "surgery",
          "MSS": "medical_special_services",
          "ICU/HDU": "intensive_care",
          "CNS": "community_nursing_services",
        };

        if (unitMappings[unit]) unit = unitMappings[unit];
        if (departmentMappings[unit]) department = departmentMappings[unit];
        if (departmentMappings[row.Unit]) department = departmentMappings[row.Unit];
        if (departmentMappings[department]) department = departmentMappings[department];

        return { unit, department };
      };

      for (const row of jsonData) {
        try {
          const empNo = row.EmployeeNo?.toString();
          if (!empNo || isNaN(empNo)) {
            stats.errors++;
            continue;
          }

          const docRef = doc(firestoreDB, "employees", empNo);
          const docSnap = await getDoc(docRef);
          const existingData = docSnap.exists() ? docSnap.data() : {};
          const isNewRecord = !docSnap.exists();

          const existingQualifications = existingData.qualifications || [];
          const newQual = row.QualificationType && row.QualificationSubType && row.CourseTitle && row.YearObtained
            ? {
                type: row.QualificationType,
                subType: row.QualificationSubType,
                courseTitle: row.CourseTitle,
                yearObtained: row.YearObtained,
              }
            : null;

          const isDuplicate = newQual
            ? existingQualifications.some(
                (q) =>
                  q.type === newQual.type &&
                  q.subType === newQual.subType &&
                  q.courseTitle === newQual.courseTitle &&
                  q.yearObtained === newQual.yearObtained
              )
            : true;

          if (newQual && isDuplicate) {
            stats.duplicatesSkipped++;
          }

          const updatedQualifications = !isDuplicate && newQual
            ? [...existingQualifications, newQual]
            : existingQualifications;

          const { unit, department } = mapDepartmentAndUnit(row);

          await setDoc(
            docRef,
            {
              department: department || existingData.department || "",
              unit: unit || existingData.unit || "",
              rank: row.Rank || existingData.rank || "",
              name: row.Name || existingData.name || "",
              employeeNo: empNo,
              appointmentDate: row.AppointmentDate || existingData.appointmentDate || "",
              rnAppointmentDate: row.RNAppointmentDate || existingData.rnAppointmentDate || "",
              qualifications: updatedQualifications,
              lastUpdated: serverTimestamp(),
            },
            { merge: true }
          );

          if (isNewRecord) {
            stats.newRecords++;
          } else {
            stats.updatedRecords++;
          }

          stats.processed++;
          const currentIndex = jsonData.indexOf(row) + 1;
          setProgress(Math.round((currentIndex / total) * 100));

          // Update stats in state for real-time display
          setImportStats({...stats});

        } catch (error) {
          console.error("Error processing row:", error);
          stats.errors++;
        }
      }

      // Show detailed completion alert
      const successMessage =
        `✅ Import Completed Successfully!\n\n` +
        `📊 Import Summary:\n` +
        `• Total rows processed: ${stats.processed}/${stats.total}\n` +
        `• New employee records: ${stats.newRecords}\n` +
        `• Updated existing records: ${stats.updatedRecords}\n` +
        `• Duplicate qualifications skipped: ${stats.duplicatesSkipped}\n` +
        `• Errors encountered: ${stats.errors}\n\n` +
        `${stats.errors > 0 ? '⚠️ Some rows had errors and were skipped.\n' : ''}` +
        `The import process is now complete!`;

      alert(successMessage);

      // Show additional success confirmation
      if (stats.newRecords > 0 || stats.updatedRecords > 0) {
        const confirmViewResults = window.confirm(
          `🎉 Data Successfully Imported!\n\n` +
          `Would you like to view the updated records now?\n\n` +
          `Click OK to go to View Results, or Cancel to stay here.`
        );

        if (confirmViewResults) {
          // You can add navigation logic here if needed
          setMessage(`✅ Import completed! ${stats.newRecords} new records, ${stats.updatedRecords} updated records.`);
        }
      }

      setMessage(`✅ Import completed! ${stats.newRecords} new records, ${stats.updatedRecords} updated records.`);
      setUploading(false);
      setProgress(0);

      // Reset file input for next import
      event.target.value = '';

    } catch (error) {
      console.error("Error during import:", error);

      // Detailed error alert
      const errorMessage =
        `❌ Import Failed!\n\n` +
        `An error occurred while processing the Excel file:\n\n` +
        `Error: ${error.message || 'Unknown error'}\n\n` +
        `Please check your file format and try again.`;

      alert(errorMessage);
      setMessage("Import failed. Please try again.");
      setUploading(false);
      setProgress(0);

      // Reset file input
      event.target.value = '';
    }
    };

    reader.readAsArrayBuffer(file);
  };

  // Fetch departments and units for user creation
  const fetchDepartmentsAndUnits = async () => {
    try {
      const departmentsSnapshot = await getDocs(collection(firestoreDB, "departments"));
      const departmentList = departmentsSnapshot.docs
        .filter((doc) => doc.id !== "archived_data") // Exclude archived_data
        .map((doc) => ({
          id: doc.id,
          name: doc.data().name,
          units: [] // Initialize empty units array
        }));

      // Fetch units for each department
      for (const dept of departmentList) {
        try {
          const unitsSnapshot = await getDocs(collection(firestoreDB, `departments/${dept.id}/units`));
          const unitList = unitsSnapshot.docs.map(unitDoc => ({
            id: unitDoc.id,
            name: unitDoc.data().name,
            departmentId: dept.id
          }));
          dept.units = unitList;
        } catch (error) {
          console.error(`Error fetching units for department ${dept.id}:`, error);
          dept.units = [];
        }
      }

      setDepartments(departmentList);
      console.log("Departments loaded:", departmentList); // Debug log

      // Flatten all units for easy access
      const allUnits = departmentList.flatMap(dept => dept.units);
      setUnits(allUnits);
      console.log("All units loaded:", allUnits); // Debug log
    } catch (error) {
      console.error("Error fetching departments and units:", error);
      setUserCreationMessage("Error loading departments and units.");
    }
  };

  // Add a new user profile to the list
  const addUserProfile = () => {
    const newProfile = {
      id: Date.now(), // Simple ID for tracking
      employeeNo: "",
      name: "",
      department: "",
      unit: "",
      rank: "",
      freshGrad: "no"
    };
    setUserProfiles([...userProfiles, newProfile]);
  };

  // Remove a user profile from the list
  const removeUserProfile = (id) => {
    setUserProfiles(userProfiles.filter(profile => profile.id !== id));
  };

  // Update a user profile field
  const updateUserProfile = (id, field, value) => {
    setUserProfiles(userProfiles.map(profile =>
      profile.id === id ? { ...profile, [field]: value } : profile
    ));
  };

  // Create multiple user profiles in the database
  const createUserProfiles = async () => {
    if (userProfiles.length === 0) {
      setUserCreationMessage("Please add at least one user profile.");
      return;
    }

    // Validate all profiles
    const invalidProfiles = userProfiles.filter(profile =>
      !profile.employeeNo || !profile.name || !profile.department || !profile.unit || !profile.rank
    );

    if (invalidProfiles.length > 0) {
      setUserCreationMessage(`Please complete all required fields for all user profiles. ${invalidProfiles.length} profile(s) are incomplete.`);
      return;
    }

    // Check for duplicate employee numbers
    const employeeNos = userProfiles.map(p => p.employeeNo);
    const duplicates = employeeNos.filter((item, index) => employeeNos.indexOf(item) !== index);
    if (duplicates.length > 0) {
      setUserCreationMessage(`Duplicate employee numbers found: ${duplicates.join(", ")}. Please ensure all employee numbers are unique.`);
      return;
    }

    const confirmCreate = window.confirm(
      `👥 Create User Profiles\n\n` +
      `You are about to create ${userProfiles.length} user profile(s).\n\n` +
      `This will add new employee records to the database.\n\n` +
      `Are you sure you want to proceed?`
    );

    if (!confirmCreate) return;

    setCreatingUsers(true);
    setUserCreationMessage("Creating user profiles...");

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    try {
      for (const profile of userProfiles) {
        try {
          // Check if employee already exists
          const docRef = doc(firestoreDB, "employees", profile.employeeNo);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            errors.push(`Employee ${profile.employeeNo} (${profile.name}) already exists`);
            errorCount++;
            continue;
          }

          // Create new employee record
          await setDoc(docRef, {
            employeeNo: profile.employeeNo,
            name: profile.name.toUpperCase(),
            department: profile.department,
            unit: profile.unit,
            rank: profile.rank,
            qualifications: [],
            freshGrad: profile.freshGrad === "yes" ? new Date().getFullYear() : null,
            lastUpdated: serverTimestamp(),
            createdAt: serverTimestamp(),
            createdBy: "admin_import"
          });

          successCount++;
        } catch (error) {
          console.error(`Error creating profile for ${profile.employeeNo}:`, error);
          errors.push(`Failed to create ${profile.employeeNo} (${profile.name}): ${error.message}`);
          errorCount++;
        }
      }

      let resultMessage = `✅ User Profile Creation Complete!\n\n`;
      resultMessage += `Successfully created: ${successCount} profile(s)\n`;
      if (errorCount > 0) {
        resultMessage += `Errors: ${errorCount} profile(s)\n\n`;
        resultMessage += `Error details:\n${errors.join("\n")}`;
      }

      alert(resultMessage);
      setUserCreationMessage(`Profile creation complete: ${successCount} created, ${errorCount} errors`);

      if (successCount > 0) {
        // Clear the form on success
        setUserProfiles([]);
      }

    } catch (error) {
      console.error("Error in bulk user creation:", error);
      setUserCreationMessage(`Error creating user profiles: ${error.message}`);
    } finally {
      setCreatingUsers(false);
    }
  };

  // Fetch staff list by department and unit
  const fetchStaffByUnit = async () => {
    if (!selectedDepartmentForFilter || !selectedUnitForFilter) {
      setRelocationMessage("Please select both department and unit to filter staff.");
      return;
    }

    setRelocationMessage("Loading staff list...");

    try {
      const employeesSnapshot = await getDocs(collection(firestoreDB, "employees"));
      const filteredStaff = [];

      employeesSnapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.department === selectedDepartmentForFilter && data.unit === selectedUnitForFilter) {
          filteredStaff.push({
            employeeNo: data.employeeNo,
            name: data.name,
            rank: data.rank,
            department: data.department,
            unit: data.unit
          });
        }
      });

      setStaffList(filteredStaff);
      setSelectedStaff("");
      setRelocationMessage(`Found ${filteredStaff.length} staff member(s) in ${selectedUnitForFilter}.`);

    } catch (error) {
      console.error("Error fetching staff:", error);
      setRelocationMessage(`Error loading staff: ${error.message}`);
    }
  };

  // Relocate selected staff to new location
  const relocateStaff = async () => {
    if (!selectedStaff || !newDepartment || !newUnit) {
      setRelocationMessage("Please select staff member and new location.");
      return;
    }

    const selectedStaffData = staffList.find(staff => staff.employeeNo === selectedStaff);
    if (!selectedStaffData) {
      setRelocationMessage("Selected staff member not found.");
      return;
    }

    const confirmRelocation = window.confirm(
      `🔄 Relocate Staff Member\n\n` +
      `Staff: ${selectedStaffData.name} (${selectedStaffData.employeeNo})\n` +
      `Current Location: ${selectedStaffData.unit}\n` +
      `New Location: ${departments.find(d => d.id === newDepartment)?.name} - ${departments.find(d => d.id === newDepartment)?.units.find(u => u.name === newUnit)?.name}\n\n` +
      `Are you sure you want to relocate this staff member?`
    );

    if (!confirmRelocation) return;

    setRelocatingStaff(true);
    setRelocationMessage("Relocating staff member...");

    try {
      const docRef = doc(firestoreDB, "employees", selectedStaff);
      await updateDoc(docRef, {
        department: newDepartment,
        unit: newUnit,
        lastUpdated: serverTimestamp()
      });

      setRelocationMessage(`✅ Successfully relocated ${selectedStaffData.name} to ${newUnit}.`);

      // Reset form
      setSelectedStaff("");
      setNewDepartment("");
      setNewUnit("");

      // Refresh staff list
      await fetchStaffByUnit();

    } catch (error) {
      console.error("Error relocating staff:", error);
      setRelocationMessage(`❌ Error relocating staff: ${error.message}`);
    } finally {
      setRelocatingStaff(false);
    }
  };

  return (
    <div style={{ maxWidth: "1000px", margin: "40px auto", padding: "20px", fontFamily: "Arial, sans-serif" }}>
      <h1 style={{ textAlign: "center", color: "#007BFF", marginBottom: "30px" }}>
        Admin Data Management
      </h1>

      {/* Excel Import Section */}
      <div style={{
        border: "1px solid #ccc",
        borderRadius: "10px",
        padding: "20px",
        marginBottom: "30px",
        backgroundColor: "#f8f9fa"
      }}>
        <h2 style={{ color: "#495057", marginBottom: "15px" }}>📊 Import Staff Records via Excel</h2>
        {uploading && (
          <div style={{ marginBottom: "10px", color: "#007BFF", fontWeight: "bold" }}>
            ⏳ Uploading and Processing Excel...
            <div style={{ marginTop: "5px", background: "#eee", height: "20px", borderRadius: "5px", overflow: "hidden" }}>
              <div
                style={{
                  width: `${progress}%`,
                  height: "100%",
                  background: "#007BFF",
                  transition: "width 0.3s ease",
                  textAlign: "center",
                  color: "white",
                  fontWeight: "bold"
                }}
              >
                {progress}%
              </div>
            </div>
          </div>
        )}
        <input
          type="file"
          accept=".xlsx, .xls"
          onChange={handleExcelImport}
          style={{
            padding: "10px",
            border: "2px solid #007BFF",
            borderRadius: "5px",
            width: "100%",
            marginBottom: "10px"
          }}
        />
        {message && <p style={{ color: "green", marginTop: "10px", fontWeight: "bold" }}>{message}</p>}
      </div>

      {/* User Profile Creation Section */}
      <div style={{
        border: "1px solid #28a745",
        borderRadius: "10px",
        padding: "20px",
        backgroundColor: "#f8fff8"
      }}>
        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
          <h2 style={{ color: "#28a745", margin: 0 }}>👥 Create User Profiles</h2>
          <button
            onClick={() => {
              setShowUserCreation(!showUserCreation);
              if (!showUserCreation && departments.length === 0) {
                fetchDepartmentsAndUnits();
              }
            }}
            style={{
              padding: "8px 16px",
              backgroundColor: showUserCreation ? "#6c757d" : "#28a745",
              color: "white",
              border: "none",
              borderRadius: "5px",
              cursor: "pointer",
              fontWeight: "bold"
            }}
          >
            {showUserCreation ? "Hide" : "Show"} User Creation
          </button>
        </div>

        {showUserCreation && (
          <div>
            <p style={{ color: "#495057", marginBottom: "20px" }}>
              Create multiple employee profiles that can be updated through the qualification form.
              These profiles will be added to the database with basic information.
            </p>

            {/* User Profiles List */}
            {userProfiles.length > 0 && (
              <div style={{ marginBottom: "20px" }}>
                <h3 style={{ color: "#495057" }}>User Profiles to Create ({userProfiles.length})</h3>
                {userProfiles.map((profile, index) => (
                  <div key={profile.id} style={{
                    border: "1px solid #dee2e6",
                    borderRadius: "8px",
                    padding: "15px",
                    marginBottom: "15px",
                    backgroundColor: "white"
                  }}>
                    <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "10px" }}>
                      <h4 style={{ margin: 0, color: "#495057" }}>Profile {index + 1}</h4>
                      <button
                        onClick={() => removeUserProfile(profile.id)}
                        style={{
                          backgroundColor: "#dc3545",
                          color: "white",
                          border: "none",
                          borderRadius: "4px",
                          padding: "5px 10px",
                          cursor: "pointer"
                        }}
                      >
                        Remove
                      </button>
                    </div>

                    <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "10px" }}>
                      <div>
                        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Employee No *</label>
                        <input
                          type="text"
                          value={profile.employeeNo}
                          onChange={(e) => updateUserProfile(profile.id, "employeeNo", e.target.value)}
                          placeholder="Enter Employee Number"
                          style={{
                            width: "100%",
                            padding: "8px",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            boxSizing: "border-box"
                          }}
                        />
                      </div>

                      <div>
                        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Name *</label>
                        <input
                          type="text"
                          value={profile.name}
                          onChange={(e) => updateUserProfile(profile.id, "name", e.target.value.toUpperCase())}
                          placeholder="Enter Full Name"
                          style={{
                            width: "100%",
                            padding: "8px",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            boxSizing: "border-box"
                          }}
                        />
                      </div>

                      <div>
                        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Department *</label>
                        <select
                          value={profile.department}
                          onChange={(e) => {
                            updateUserProfile(profile.id, "department", e.target.value);
                            updateUserProfile(profile.id, "unit", ""); // Reset unit when department changes
                          }}
                          style={{
                            width: "100%",
                            padding: "8px",
                            border: "1px solid #ccc",
                            borderRadius: "4px"
                          }}
                        >
                          <option value="">-- Select Department --</option>
                          {departments.map(dept => (
                            <option key={dept.id} value={dept.id}>{dept.name}</option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Unit *</label>
                        <select
                          value={profile.unit}
                          onChange={(e) => updateUserProfile(profile.id, "unit", e.target.value)}
                          disabled={!profile.department}
                          style={{
                            width: "100%",
                            padding: "8px",
                            border: "1px solid #ccc",
                            borderRadius: "4px",
                            backgroundColor: !profile.department ? "#f5f5f5" : "white"
                          }}
                        >
                          <option value="">-- Select Unit --</option>
                          {departments.find(d => d.id === profile.department)?.units.map(unit => (
                            <option key={unit.id} value={unit.name}>{unit.name}</option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Rank *</label>
                        <select
                          value={profile.rank}
                          onChange={(e) => updateUserProfile(profile.id, "rank", e.target.value)}
                          style={{
                            width: "100%",
                            padding: "8px",
                            border: "1px solid #ccc",
                            borderRadius: "4px"
                          }}
                        >
                          <option value="">-- Select Rank --</option>
                          <option value="SNO">SNO</option>
                          <option value="DOM">DOM</option>
                          <option value="WM">WM</option>
                          <option value="ANC">ANC</option>
                          <option value="APN">APN</option>
                          <option value="RN">RN</option>
                          <option value="EN">EN</option>
                          <option value="LOCUMRN">LocumRN</option>
                          <option value="LOCUMEN">LocumEN</option>
                          <option value="TUNS">TUNS</option>
                        </select>
                      </div>

                      <div>
                        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Graduated This Year?</label>
                        <select
                          value={profile.freshGrad}
                          onChange={(e) => updateUserProfile(profile.id, "freshGrad", e.target.value)}
                          style={{
                            width: "100%",
                            padding: "8px",
                            border: "1px solid #ccc",
                            borderRadius: "4px"
                          }}
                        >
                          <option value="no">No</option>
                          <option value="yes">Yes</option>
                        </select>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Action Buttons */}
            <div style={{ display: "flex", gap: "10px", marginBottom: "15px", flexWrap: "wrap" }}>
              <button
                onClick={addUserProfile}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#007BFF",
                  color: "white",
                  border: "none",
                  borderRadius: "5px",
                  cursor: "pointer",
                  fontWeight: "bold"
                }}
              >
                + Add User Profile
              </button>

              {userRole === "admin" && (
                <button
                  onClick={() => setShowStaffRelocation(!showStaffRelocation)}
                  style={{
                    padding: "10px 20px",
                    backgroundColor: "#17a2b8",
                    color: "white",
                    border: "none",
                    borderRadius: "5px",
                    cursor: "pointer",
                    fontWeight: "bold"
                  }}
                >
                  🔄 {showStaffRelocation ? "Hide" : "Show"} Staff Relocation
                </button>
              )}

              {userProfiles.length > 0 && (
                <button
                  onClick={createUserProfiles}
                  disabled={creatingUsers}
                  style={{
                    padding: "10px 20px",
                    backgroundColor: creatingUsers ? "#6c757d" : "#28a745",
                    color: "white",
                    border: "none",
                    borderRadius: "5px",
                    cursor: creatingUsers ? "not-allowed" : "pointer",
                    fontWeight: "bold"
                  }}
                >
                  {creatingUsers ? "Creating..." : `Create ${userProfiles.length} Profile(s)`}
                </button>
              )}
            </div>

            {userCreationMessage && (
              <p style={{
                color: userCreationMessage.includes("Error") || userCreationMessage.includes("error") ? "red" : "green",
                fontWeight: "bold",
                padding: "10px",
                backgroundColor: userCreationMessage.includes("Error") || userCreationMessage.includes("error") ? "#ffebee" : "#e8f5e8",
                borderRadius: "5px"
              }}>
                {userCreationMessage}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Staff Relocation Section - Admin Only */}
      {userRole === "admin" && showStaffRelocation && (
        <div style={{
          border: "1px solid #17a2b8",
          borderRadius: "10px",
          padding: "20px",
          marginTop: "20px",
          backgroundColor: "#f0f8ff"
        }}>
          <h2 style={{ color: "#17a2b8", marginBottom: "20px" }}>🔄 Staff Relocation</h2>
          <p style={{ color: "#495057", marginBottom: "20px" }}>
            Select existing staff by department/unit and relocate them to a new location.
          </p>

          {/* Filter Section */}
          <div style={{
            backgroundColor: "white",
            padding: "15px",
            borderRadius: "8px",
            marginBottom: "20px",
            border: "1px solid #dee2e6"
          }}>
            <h3 style={{ color: "#495057", marginBottom: "15px" }}>Filter Staff by Current Location</h3>
            <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr 1fr", gap: "15px", alignItems: "end" }}>
              <div>
                <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Department</label>
                <select
                  value={selectedDepartmentForFilter}
                  onChange={(e) => {
                    setSelectedDepartmentForFilter(e.target.value);
                    setSelectedUnitForFilter("");
                    setStaffList([]);
                    setSelectedStaff("");
                  }}
                  style={{
                    width: "100%",
                    padding: "8px",
                    border: "1px solid #ccc",
                    borderRadius: "4px"
                  }}
                >
                  <option value="">-- Select Department --</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.id}>{dept.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Unit</label>
                <select
                  value={selectedUnitForFilter}
                  onChange={(e) => {
                    setSelectedUnitForFilter(e.target.value);
                    setStaffList([]);
                    setSelectedStaff("");
                  }}
                  disabled={!selectedDepartmentForFilter}
                  style={{
                    width: "100%",
                    padding: "8px",
                    border: "1px solid #ccc",
                    borderRadius: "4px",
                    backgroundColor: !selectedDepartmentForFilter ? "#f5f5f5" : "white"
                  }}
                >
                  <option value="">-- Select Unit --</option>
                  {departments.find(d => d.id === selectedDepartmentForFilter)?.units.map(unit => (
                    <option key={unit.id} value={unit.name}>{unit.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <button
                  onClick={fetchStaffByUnit}
                  disabled={!selectedDepartmentForFilter || !selectedUnitForFilter}
                  style={{
                    padding: "8px 16px",
                    backgroundColor: (!selectedDepartmentForFilter || !selectedUnitForFilter) ? "#6c757d" : "#007BFF",
                    color: "white",
                    border: "none",
                    borderRadius: "4px",
                    cursor: (!selectedDepartmentForFilter || !selectedUnitForFilter) ? "not-allowed" : "pointer",
                    fontWeight: "bold"
                  }}
                >
                  Load Staff
                </button>
              </div>
            </div>
          </div>

          {/* Staff Selection and Relocation */}
          {staffList.length > 0 && (
            <div style={{
              backgroundColor: "white",
              padding: "15px",
              borderRadius: "8px",
              marginBottom: "20px",
              border: "1px solid #dee2e6"
            }}>
              <h3 style={{ color: "#495057", marginBottom: "15px" }}>Select Staff and New Location</h3>
              <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr 1fr 1fr", gap: "15px", alignItems: "end" }}>
                <div>
                  <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>Staff Member</label>
                  <select
                    value={selectedStaff}
                    onChange={(e) => setSelectedStaff(e.target.value)}
                    style={{
                      width: "100%",
                      padding: "8px",
                      border: "1px solid #ccc",
                      borderRadius: "4px"
                    }}
                  >
                    <option value="">-- Select Staff --</option>
                    {staffList.map(staff => (
                      <option key={staff.employeeNo} value={staff.employeeNo}>
                        {staff.name} ({staff.employeeNo}) - {staff.rank}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>New Department</label>
                  <select
                    value={newDepartment}
                    onChange={(e) => {
                      setNewDepartment(e.target.value);
                      setNewUnit("");
                    }}
                    style={{
                      width: "100%",
                      padding: "8px",
                      border: "1px solid #ccc",
                      borderRadius: "4px"
                    }}
                  >
                    <option value="">-- Select Department --</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.id}>{dept.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>New Unit</label>
                  <select
                    value={newUnit}
                    onChange={(e) => setNewUnit(e.target.value)}
                    disabled={!newDepartment}
                    style={{
                      width: "100%",
                      padding: "8px",
                      border: "1px solid #ccc",
                      borderRadius: "4px",
                      backgroundColor: !newDepartment ? "#f5f5f5" : "white"
                    }}
                  >
                    <option value="">-- Select Unit --</option>
                    {departments.find(d => d.id === newDepartment)?.units.map(unit => (
                      <option key={unit.id} value={unit.name}>{unit.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <button
                    onClick={relocateStaff}
                    disabled={!selectedStaff || !newDepartment || !newUnit || relocatingStaff}
                    style={{
                      padding: "8px 16px",
                      backgroundColor: (!selectedStaff || !newDepartment || !newUnit || relocatingStaff) ? "#6c757d" : "#28a745",
                      color: "white",
                      border: "none",
                      borderRadius: "4px",
                      cursor: (!selectedStaff || !newDepartment || !newUnit || relocatingStaff) ? "not-allowed" : "pointer",
                      fontWeight: "bold"
                    }}
                  >
                    {relocatingStaff ? "Relocating..." : "Relocate"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {relocationMessage && (
            <p style={{
              color: relocationMessage.includes("Error") || relocationMessage.includes("❌") ? "red" : "green",
              fontWeight: "bold",
              padding: "10px",
              backgroundColor: relocationMessage.includes("Error") || relocationMessage.includes("❌") ? "#ffebee" : "#e8f5e8",
              borderRadius: "5px"
            }}>
              {relocationMessage}
            </p>
          )}
        </div>
      )}
    </div>
  );
}

export default ImportData;
