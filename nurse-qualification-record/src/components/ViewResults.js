import React, { useState, useEffect } from "react";
import { firestoreDB, auth } from "../services/firebase";
import { collection, getDocs } from "firebase/firestore";
import { utils, writeFile } from "xlsx";
import { doc, getDoc, setDoc, deleteDoc, serverTimestamp } from "firebase/firestore";




function ViewResults({ userRole, department, unit, userDepartments, userUnits }) {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [departments, setDepartments] = useState([]);
  const [units, setUnits] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(department || "all");
  const [selectedUnit, setSelectedUnit] = useState(unit || "all");
  const [selectedQualification, setSelectedQualification] = useState("all");
  const [selectedSubType, setSelectedSubType] = useState("all");
  const [selectedCourseTitle, setSelectedCourseTitle] = useState("all");
  const [availableCourseTitles, setAvailableCourseTitles] = useState([]);
  const [filteredInstructors, setFilteredInstructors] = useState(null);
  const [filteredResults, setFilteredResults] = useState([]);
  const [filteredCRMInstructor, setFilteredCRMInstructor] = useState(null);
  const [selectedQualificationType, setSelectedQualificationType] = useState("");
  const [selectedQualificationSubtype, setSelectedQualificationSubtype] = useState("");
  const [selectedQualifications, setSelectedQualifications] = useState([]);
  const [selectedSubtypes, setSelectedSubtypes] = useState([]);
  const [showCheckboxes, setShowCheckboxes] = useState(false);
  const [qualificationSubtypes, setQualificationSubtypes] = useState({});
  const [qualificationTypes, setQualificationTypes] = useState([{ id: "all", name: "ALL Qualifications" }]);
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetMessage, setResetMessage] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [displayedResults, setDisplayedResults] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [editingRemark, setEditingRemark] = useState(null); // employeeId
  const [remarkText, setRemarkText] = useState("");
  const [staffStats, setStaffStats] = useState({ total: 0, withData: 0 });

  const tableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    fontSize: "14px",
    border: "1px solid #ddd",
    borderRadius: "10px",
    overflow: "hidden",
    boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
  };

  const headerRowStyle = {
    backgroundColor: "#007BFF",
    color: "white",
    textAlign: "left",
  };

  const headerCellStyle = {
    padding: "12px",
    fontWeight: "bold",
    borderBottom: "2px solid #ddd",
    cursor: "pointer",
    userSelect: "none",
    transition: "background-color 0.2s",
  };

  const getSortableHeaderStyle = (isActive) => ({
    ...headerCellStyle,
    backgroundColor: isActive ? "#0056b3" : "transparent",
    "&:hover": {
      backgroundColor: "#0056b3",
    }
  });

  const evenRowStyle = {
    backgroundColor: "#f9f9f9",
  };

  const oddRowStyle = {
    backgroundColor: "#ffffff",
  };

  const cellStyle = {
    padding: "10px",
    borderBottom: "1px solid #ddd",
    textAlign: "left",
  };

  const hoverRowStyle = {
    backgroundColor: "#f1f1f1",
  };

  /* ✅ Sub-Table Styles */
  const subTableStyle = {
    width: "100%",
    borderCollapse: "collapse",
    marginTop: "5px",
    backgroundColor: "#f9f9f9",
  };

  const subHeaderRowStyle = {
    backgroundColor: "#ddd",
  };

  const subHeaderCellStyle = {
    padding: "8px",
    fontWeight: "bold",
    borderBottom: "1px solid #aaa",
  };

  const subRowStyle = {
    borderBottom: "1px solid #ddd",
  };

  const subCellStyle = {
    padding: "8px",
  };


  useEffect(() => {
    const fetchDepartments = async () => {
      const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
      const departmentList = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        name: doc.data().name
      }));
      setDepartments([{ id: "all", name: "ALL Departments" }, ...departmentList]);
    };

    const fetchUnits = async () => {
      if (selectedDepartment !== "all") {
        const querySnapshot = await getDocs(collection(firestoreDB, `departments/${selectedDepartment}/units`));
        const unitList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name
        }));
        setUnits([{ id: "all", name: "ALL Units" }, ...unitList]);
      } else {
        setUnits([{ id: "all", name: "ALL Units" }]);
        setSelectedUnit("all"); // Ensure unit filter resets properly
      }
    };

    const fetchQualifications = async () => {
      const querySnapshot = await getDocs(collection(firestoreDB, "qualifications"));
      const types = querySnapshot.docs.map((doc) => {
        const data = doc.data();
        return {
          id: data.type?.toLowerCase().replace(/\s+/g, "_") || doc.id,
          name: data.title || "Unnamed"
        };
      });
      setQualificationTypes([{ id: "all", name: "ALL Qualifications" }, ...types]);
      if (!types.find(q => q.id === selectedQualification)) {
        setSelectedQualification("all");
      }
    };

    fetchDepartments();
    fetchUnits();
    fetchQualifications();
  }, [selectedDepartment]);

  const toggleCheckbox = (option) => {
    setSelectedQualifications((prev) =>
      prev.includes(option)
        ? prev.filter((item) => item !== option)
        : [...prev, option]
    );
  };
  const fetchResults = async () => {
    setLoading(true);
    const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
    let records = querySnapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
      // appointmentDate: doc.data().appointmentDate || null,
      // rnAppointmentDate: doc.data().rnAppointmentDate || null,
    }));

    if (selectedQualifications.length > 0) {
      records = records.filter((record) =>
        record.qualifications?.some((qual) => selectedQualifications.includes(qual.subType))
      );
    }

    setResults(records);
    setLoading(false);
  };

  useEffect(() => {
    fetchResults();
  }, [selectedQualifications]);




  // Log the qualificationSubtypes structure
  useEffect(() => {
    console.log("🔥 Qualification Subtypes Structure:", JSON.stringify(qualificationSubtypes, null, 2));
  }, [qualificationSubtypes]);

  // Log the loaded units for debugging
  useEffect(() => {
    console.log("📦 Units loaded:", units);
  }, [units]);


  // Fetch qualification types and subtypes
  useEffect(() => {
    const fetchQualifications = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "qualifications"));
        const subtypesMap = {};

        querySnapshot.forEach((doc) => {
          const { type, options } = doc.data();
          if (type && options) {
            subtypesMap[type] = options;
          }
        });

        console.log("🔥 Subtypes Map:", subtypesMap);
        setQualificationSubtypes(subtypesMap);
      } catch (error) {
        console.error("❌ Error fetching qualification subtypes:", error);
      }
    };

    fetchQualifications();
  }, []);
  // Handle checkbox change
  const handleCheckboxChange = (type, option) => {
    setSelectedSubtypes((prev) => {
      const newSubtypes = { ...prev };
      if (!newSubtypes[type]) newSubtypes[type] = [];

      if (newSubtypes[type].includes(option)) {
        newSubtypes[type] = newSubtypes[type].filter((item) => item !== option);
      } else {
        newSubtypes[type].push(option);
      }

      return newSubtypes;
    });
  };

  // Filter records based on selected qualifications
  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
        let records = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          // appointmentDate: doc.data().appointmentDate || null,
          // rnAppointmentDate: doc.data().rnAppointmentDate || null,
          lastUpdated: doc.data().lastUpdated
            ? (doc.data().lastUpdated.toDate
              ? doc.data().lastUpdated.toDate().toLocaleString()
              : new Date(doc.data().lastUpdated.seconds * 1000).toLocaleString())
            : "N/A",
        }));

        // Filter by selected qualifications
        if (selectedQualifications.length > 0) {
          records = records.filter((record) =>
            record.qualifications?.some((qual) =>
              selectedQualifications.includes(qual.type)
            )
          );
        }

        setResults(records);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    fetchResults();
  }, [selectedQualifications]);

  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
        const departmentList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name
        }));
        setDepartments([{ id: "all", name: "ALL Departments" }, ...departmentList]);
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };

    const fetchUnits = async () => {
      if (selectedDepartment !== "all") {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${selectedDepartment}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name
          }));
          setUnits([{ id: "all", name: "ALL Units" }, ...unitList]);
        } catch (error) {
          console.error("Error fetching units:", error);
        }
      } else {
        setUnits([{ id: "all", name: "ALL Units" }]);
        setSelectedUnit("all"); // Ensure unit filter resets properly
      }
    };

    const fetchQualifications = async () => {
      try {
        const docRef = doc(firestoreDB, "qualifications", "types");
        const docSnap = await getDoc(docRef);
        if (docSnap.exists()) {
          const data = docSnap.data();
          setQualificationTypes(data.types || []);
          setQualificationSubtypes(data.subtypes || {});
        }
      } catch (error) {
        console.error("Error fetching qualifications:", error);
      }
    };

    fetchDepartments();
    fetchUnits();
    fetchQualifications();
  }, [selectedDepartment]);

  // Fetch employees with applied filters
  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
        let records = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
          // appointmentDate: doc.data().appointmentDate || null,
          // rnAppointmentDate: doc.data().rnAppointmentDate || null,
          lastUpdated: doc.data().lastUpdated?.toDate().toLocaleString() || "N/A"
        }));

        // Filter by department
        if (selectedDepartment !== "all") {
          records = records.filter((record) => record.department === selectedDepartment);
        }

        // Filter by unit
        if (selectedUnit !== "all") {
          records = records.filter((record) => record.unit === selectedUnit);
        }

        // Filter by selected qualification type
        if (selectedQualificationType) {
          records = records.filter((record) =>
            record.qualifications?.some((qual) => qual.type === selectedQualificationType)
          );
        }

        // Filter by selected qualification subtype
        if (selectedQualificationSubtype) {
          records = records.filter((record) =>
            record.qualifications?.some((qual) => qual.subType === selectedQualificationSubtype)
          );
        }

        setResults(records);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    if (departments.length > 1 && units.length > 0) {
      fetchResults();
    }
  }, [selectedDepartment, selectedUnit, selectedQualification, filteredInstructors, departments, units]);

  // Sorting functions
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const getSortIndicator = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return ' ↕️'; // Both arrows when not sorted
    }
    return sortConfig.direction === 'asc' ? ' ↑' : ' ↓';
  };

  // Apply sorting to results
  const sortedResults = React.useMemo(() => {
    if (!sortConfig.key) return results;

    return [...results].sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      // Handle special cases for different column types
      if (sortConfig.key === 'lastUpdated') {
        // Sort by raw timestamp for accurate sorting
        aValue = a.rawLastUpdated?.seconds || 0;
        bValue = b.rawLastUpdated?.seconds || 0;
      } else if (sortConfig.key === 'qualificationCount') {
        aValue = a.qualifications?.length || 0;
        bValue = b.qualifications?.length || 0;
      } else if (sortConfig.key === 'employeeNo') {
        // Sort employee numbers numerically if possible
        const aNum = parseInt(aValue) || 0;
        const bNum = parseInt(bValue) || 0;
        if (!isNaN(aNum) && !isNaN(bNum)) {
          aValue = aNum;
          bValue = bNum;
        } else {
          aValue = (aValue || '').toString().toLowerCase();
          bValue = (bValue || '').toString().toLowerCase();
        }
      } else {
        // Convert to string for comparison, handle null/undefined
        aValue = (aValue || '').toString().toLowerCase();
        bValue = (bValue || '').toString().toLowerCase();
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [results, sortConfig]);

  // Search filtering effect
  useEffect(() => {
    if (!searchTerm.trim()) {
      setDisplayedResults(sortedResults);
    } else {
      const filtered = sortedResults.filter((record) => {
        const searchLower = searchTerm.toLowerCase();
        const nameMatch = record.name && record.name.toLowerCase().includes(searchLower);
        const employeeNoMatch = record.employeeNo && record.employeeNo.toLowerCase().includes(searchLower);
        return nameMatch || employeeNoMatch;
      });
      setDisplayedResults(filtered);
    }
  }, [sortedResults, searchTerm]);

  // Calculate staff statistics whenever displayed results change (excluding TUNS rank)
  useEffect(() => {
    if (userRole === "admin") {
      // Filter out staff with TUNS rank
      const filteredForStats = displayedResults.filter(record =>
        record.rank !== "TUNS"
      );

      const total = filteredForStats.length;
      const withData = filteredForStats.filter(record =>
        record.qualifications && record.qualifications.length > 0
      ).length;

      setStaffStats({ total, withData });
    }
  }, [displayedResults, userRole]);

  //const qualificationTypes = [
  // { id: "all", name: "ALL Qualifications" },
  //  { id: "bachelor", name: "Bachelor" },
  //   { id: "master", name: "Master" },
  //   { id: "phd_other", name: "PHD/Other" },
  //   { id: "specialty_training", name: "Specialty Training" },
  //    { id: "resuscitation_training", name: "Resuscitation Training" },
  //    { id: "ventilator_training", name: "Ventilator Training" },
  ///    { id: "crm_sim_training", name: "CRM/Sim Training" },
  //  ];

  // Get available subtypes for the selected qualification type
  const getAvailableSubtypes = () => {
    if (selectedQualification === "all") {
      // Get all unique subtypes from all qualification types
      const allSubtypes = new Set();
      Object.values(qualificationSubtypes).forEach(subtypes => {
        if (Array.isArray(subtypes)) {
          subtypes.forEach(subtype => allSubtypes.add(subtype));
        }
      });
      return Array.from(allSubtypes).sort();
    } else {
      // Find the qualification type by ID and get its corresponding type name for subtypes
      const qualType = qualificationTypes.find(q => q.id === selectedQualification);
      if (qualType) {
        // Create a mapping from title to type for common mismatches
        const titleToTypeMap = {
          "Bachelor Degree": "Bachelor",
          "Master Degree": "Master",
          "PHD/ Doctoral Degree": "PHD/ Other",
          "Crew Resource Management/Simulation Training": "CRM/Sim Training",
          "Ventilator Training": "Ventilator Training (Please refer to the list)"
        };

        // Try to find subtypes using the title first, then the mapped type, then the name directly
        let subtypes = qualificationSubtypes[qualType.name] ||
                      qualificationSubtypes[titleToTypeMap[qualType.name]] ||
                      qualificationSubtypes[qualType.id];

        if (subtypes && Array.isArray(subtypes)) {
          return subtypes;
        }
      }
      return [];
    }
  };

  // Reset subtype when qualification type changes
  useEffect(() => {
    setSelectedSubType("all");
  }, [selectedQualification]);

  // Fetch course titles when subtype changes
  useEffect(() => {
    const fetchCourseTitles = async () => {
      if (selectedSubType === "PRCC - Post-registration Certificate Course ") {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, "prccCourses"));
          const courses = querySnapshot.docs.map(doc => doc.data().title || doc.data().name || doc.id);
          setAvailableCourseTitles(courses.sort());
        } catch (error) {
          console.error("Error fetching PRCC courses:", error);
          setAvailableCourseTitles([]);
        }
      } else if (selectedSubType === "HKANM Fellowship Program") {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, "fellewshipTitles"));
          const fellowships = querySnapshot.docs.map(doc => doc.data().title || doc.data().name || doc.id);
          setAvailableCourseTitles(fellowships.sort());
        } catch (error) {
          console.error("Error fetching fellowship titles:", error);
          setAvailableCourseTitles([]);
        }
      } else {
        setAvailableCourseTitles([]);
      }

      // Reset course title selection when subtype changes
      setSelectedCourseTitle("all");
    };

    fetchCourseTitles();
  }, [selectedSubType]);


  // last update cell style
  const getLastUpdatedCellStyle = (lastUpdated, qualifications) => {
    const baseStyle = {
      padding: "8px 12px",
      borderBottom: "1px solid #ddd",
      textAlign: "center",
      borderRadius: "20px",
      fontSize: "13px",
      fontWeight: "bold",
      boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
    };

    if (!lastUpdated) {
      return { ...baseStyle, backgroundColor: "#f0f0f0", color: "#888" }; // Gray if no timestamp
    }

    if (!qualifications || qualifications.length === 0) {
      return { ...baseStyle, backgroundColor: "#fff8e1", color: "#ff9800" }; // Yellow if no qualifications
    }

    // ✅ Convert Firestore Timestamp to JavaScript Date
    let lastUpdatedDate;
    if (lastUpdated?.toDate) {
      lastUpdatedDate = lastUpdated.toDate();
    } else if (lastUpdated?.seconds) {
      lastUpdatedDate = new Date(lastUpdated.seconds * 1000);
    } else {
      lastUpdatedDate = new Date(lastUpdated); // fallback for string-based
    }

    const now = new Date();
    const diffInDays = Math.floor((now - lastUpdatedDate) / (1000 * 60 * 60 * 24));

    return diffInDays > 365
      ? { ...baseStyle, backgroundColor: "#ffebee", color: "#c62828" } // 🔴 Over a year old
      : { ...baseStyle, backgroundColor: "#e0f7fa", color: "#00796b" }; // 🩵 Updated within a year
  };




  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "departments"));
        const departmentList = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          name: doc.data().name
        }));

        setDepartments([{ id: "all", name: "ALL Departments" }, ...departmentList]);
      } catch (error) {
        console.error("Error fetching departments:", error);
      }
    };

    fetchDepartments();
  }, []);

  useEffect(() => {
    const fetchUnits = async () => {
      if (selectedDepartment !== "all") {
        try {
          const querySnapshot = await getDocs(collection(firestoreDB, `departments/${selectedDepartment}/units`));
          const unitList = querySnapshot.docs.map((doc) => ({
            id: doc.id,
            name: doc.data().name
          }));

          setUnits([{ id: "all", name: "ALL Units" }, ...unitList]);
        } catch (error) {
          console.error("Error fetching units:", error);
        }
      } else {
        setUnits([{ id: "all", name: "ALL Units" }]);
        setSelectedUnit("all"); // Ensure unit filter resets properly
      }
    };

    fetchUnits();
  }, [selectedDepartment]);

  useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const querySnapshot = await getDocs(collection(firestoreDB, "employees"));
        let records = querySnapshot.docs.map((doc) => {
          const data = doc.data();
          return {
            id: doc.id,
            ...data,
            departmentName: departments.find(d => d.id === data.department)?.name || data.department || "N/A",
            unitName: units.find(u => u.id === data.unit)?.name || data.unit || "N/A",
            rawLastUpdated: data.lastUpdated || null,
lastUpdated: data.lastUpdated
  ? (data.lastUpdated.toDate
      ? data.lastUpdated.toDate().toLocaleString()
      : new Date(data.lastUpdated.seconds * 1000).toLocaleString())
  : "N/A",
          };
        });

        // **Restrict by User Role **
        console.log("🔍 Debug - User Role:", userRole);
        console.log("🔍 Debug - User Departments:", userDepartments);
        console.log("🔍 Debug - User Units:", userUnits);
        console.log("🔍 Debug - Total records before filtering:", records.length);

        if (userRole === "department") {
          // Handle both old single department and new multiple departments
          if (userDepartments && userDepartments.length > 0) {
            // New multiple departments structure
            console.log("🔍 Debug - Filtering by multiple departments:", userDepartments);
            records = records.filter((record) => {
              const isIncluded = userDepartments.includes(record.department);
              if (isIncluded) {
                console.log("✅ Including record:", record.employeeNo, record.department);
              }
              return isIncluded;
            });
          } else if (department) {
            // Legacy single department structure
            console.log("🔍 Debug - Filtering by single department:", department);
            records = records.filter((record) => record.department === department);
          }
        } else if (userRole === "unit") {
          // Handle both old single unit and new multiple units
          if (userUnits && userUnits.length > 0) {
            // New multiple units structure
            console.log("🔍 Debug - Filtering by multiple units:", userUnits);
            records = records.filter((record) => {
              // userUnits contains "departmentId-unitId" format
              const recordUnitKey = `${record.department}-${record.unit}`;
              console.log("🔍 Debug - Checking record:", record.employeeNo, "Department:", record.department, "Unit:", record.unit, "Key:", recordUnitKey);
              const isIncluded = userUnits.includes(recordUnitKey);
              if (isIncluded) {
                console.log("✅ Including record:", record.employeeNo, recordUnitKey);
              } else {
                console.log("❌ Excluding record:", record.employeeNo, recordUnitKey, "not in", userUnits);
              }
              return isIncluded;
            });
          } else if (unit) {
            // Legacy single unit structure
            console.log("🔍 Debug - Filtering by single unit:", unit);
            records = records.filter((record) => record.unit === unit);
          }
        }

        console.log("🔍 Debug - Total records after filtering:", records.length);

        // **Filter by Department**
        if (selectedDepartment !== "all") {
          records = records.filter((record) => record.department === selectedDepartment);
        }

        // **Filter by Unit**
        if (selectedUnit && selectedUnit !== "all") {
          const selectedUnitName = units.find(u => u.id === selectedUnit)?.name;
          if (selectedUnitName) {
            records = records.filter((record) => record.unit === selectedUnitName);
          }
        }

        // **Filter by Qualification Type**
        if (selectedQualification !== "all") {
          records = records
            .map((record) => ({
              ...record,
              qualifications: record.qualifications?.filter(
                (qual) =>
                  qual.type &&
                  qual.type.toLowerCase().replace(/\s+/g, "_") === selectedQualification
              ),
            }))
            .filter((record) => record.qualifications && record.qualifications.length > 0);
        }

        // **Filter by Qualification SubType**
        if (selectedSubType !== "all") {
          records = records
            .map((record) => ({
              ...record,
              qualifications: record.qualifications?.filter(
                (qual) => {
                  // If we're also filtering by qualification type, only filter subtypes within that type
                  if (selectedQualification !== "all") {
                    return qual.subType === selectedSubType;
                  } else {
                    // If no specific type is selected, filter subtypes across all types
                    return qual.subType === selectedSubType;
                  }
                }
              ),
            }))
            .filter((record) => record.qualifications && record.qualifications.length > 0);
        }

        // **Filter by Course Title (for PRCC and Fellowship programs)**
        if (selectedCourseTitle !== "all") {
          records = records
            .map((record) => ({
              ...record,
              qualifications: record.qualifications?.filter(
                (qual) => {
                  // Check both courseTitle and fellowshipTitle fields
                  const courseTitle = qual.courseTitle || qual.fellowshipTitle || "";
                  return courseTitle === selectedCourseTitle;
                }
              ),
            }))
            .filter((record) => record.qualifications && record.qualifications.length > 0);
        }

        // Sort qualifications alphabetically by type, then by subType
        records = records.map((record) => ({
          ...record,
          qualifications: record.qualifications?.sort((a, b) => {
            const typeA = a.type?.toLowerCase() || "";
            const typeB = b.type?.toLowerCase() || "";
            const subTypeA = a.subType?.toLowerCase() || "";
            const subTypeB = b.subType?.toLowerCase() || "";

            if (typeA < typeB) return -1;
            if (typeA > typeB) return 1;
            return subTypeA.localeCompare(subTypeB);
          }) || [],
        }));

        // **Filter for BLS Instructor / AED 123 Instructor**
        if (filteredInstructors) {
          records = records.filter((record) =>
            record.qualifications?.some(
              (qual) =>
                qual.type === "Resuscitation Training" &&
                qual.subType === filteredInstructors
            )
          );
        }



        setResults(records);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching results:", error);
        setLoading(false);
      }
    };

    if (departments.length > 1 && units.length > 0) {
      fetchResults();
    }
  }, [selectedDepartment, selectedUnit, selectedQualification, selectedSubType, selectedCourseTitle, filteredInstructors, departments, units]);





  













  const exportRawToExcel = () => {
  const MAX_QUALIFICATIONS = 10;

  const flattenedRaw = displayedResults.map((record) => {
    let lastUpdatedStr = "N/A";
    if (record.lastUpdated && record.lastUpdated.seconds) {
      lastUpdatedStr = new Date(record.lastUpdated.seconds * 1000).toLocaleString();
    }
 
    const qualificationData = {};
    const qualifications = record.qualifications || [];
 
    for (let i = 0; i < MAX_QUALIFICATIONS; i++) {
      const qual = qualifications[i] || {};
      qualificationData[`Qualification_${i + 1}_Type`] = qual.type || "N/A";
      qualificationData[`Qualification_${i + 1}_SubType`] = qual.subType || "N/A";
      qualificationData[`Qualification_${i + 1}_CourseTitle`] = qual.courseTitle || "N/A";
      qualificationData[`Qualification_${i + 1}_FellowshipTitle`] = qual.fellowshipTitle || "N/A";
      qualificationData[`Qualification_${i + 1}_YearObtained`] = qual.yearObtained || "N/A";
    }
 
    return {
      EmployeeNo: record.employeeNo || "N/A",
      Name: record.name || "N/A",
      Department: record.department || "N/A",
      Unit: record.unit || "N/A",
      Rank: record.rank || "N/A",
      GraduatedThisYear: record.freshGrad || "N/A", 
      LastUpdated: lastUpdatedStr,
      ...qualificationData,
      // "Date of Present Rank Appointment": record.appointmentDate || "N/A",
      // "Date of RN Appointment in HA": record.rnAppointmentDate || "N/A",
    };
  });
 
  const worksheet = utils.json_to_sheet(flattenedRaw);
  const workbook = utils.book_new();
  utils.book_append_sheet(workbook, worksheet, "Raw Records");
  writeFile(workbook, "Nurse_Training_Raw_Data.xlsx");
  };
  const exportToExcel = () => {
  // 1. Gather all unique qualification fields
  const allQualificationFields = new Set();
  displayedResults.forEach(record => {
    (record.qualifications || []).forEach(qual => {
      const field = `${qual.type} - ${qual.subType}`;
      allQualificationFields.add(field);
    });
  });

  // Convert to array and sort alphabetically
  const qualificationColumns = Array.from(allQualificationFields).sort();

  // 2. Flatten the records using standardized columns
  const flattenedResults = displayedResults.map((record) => {
    let lastUpdatedStr = "N/A";
    if (record.lastUpdated && record.lastUpdated.seconds) {
      lastUpdatedStr = new Date(record.lastUpdated.seconds * 1000).toLocaleString();
    }

    // Create a map of this user's latest qualifications
    const qualMap = new Map();
    (record.qualifications || []).forEach(qual => {
      const key = `${qual.type} - ${qual.subType}`;
      const existing = qualMap.get(key);
      if (!existing || (qual.yearObtained && (!existing.yearObtained || parseInt(qual.yearObtained) > parseInt(existing.yearObtained)))) {
        qualMap.set(key, qual);
      }
    });

    // Build row with all standard fields using yearObtained
    const qualificationData = {};
    qualificationColumns.forEach(field => {
      const qual = qualMap.get(field);
      qualificationData[field] = qual?.yearObtained || "N/A";
    });

    return {
      EmployeeNo: record.employeeNo || "N/A",
      Name: record.name || "N/A",
      Department: record.department || "N/A",
      Unit: record.unit || "N/A",
      Rank: record.rank || "N/A",
      GraduatedThisYear: record.freshGrad || "N/A", 
      LastUpdated: lastUpdatedStr,
      ...qualificationData,
      // "Date of Present Rank Appointment": record.appointmentDate || "N/A",
      // "Date of RN Appointment in HA": record.rnAppointmentDate || "N/A",
    };
  });

    // ✅ Convert JSON to Excel format
    const worksheet = utils.json_to_sheet(flattenedResults);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, "Employee Records");

    // ✅ Save the Excel file
    writeFile(workbook, "Nurse_Training_Records.xlsx");
  };

  // Remark management functions (Admin only)
  const startEditingRemark = (employeeId, currentRemark = "") => {
    if (userRole !== "admin") {
      alert("❌ Access Denied!\n\nOnly administrators can edit remarks.");
      return;
    }
    setEditingRemark(employeeId);
    setRemarkText(currentRemark);
  };

  const cancelEditingRemark = () => {
    setEditingRemark(null);
    setRemarkText("");
  };

  const saveRemark = async () => {
    if (!editingRemark) return;

    try {
      const employeeId = editingRemark;

      // Get current employee data
      const employeeRef = doc(firestoreDB, "employees", employeeId);
      const employeeSnap = await getDoc(employeeRef);

      if (!employeeSnap.exists()) {
        alert("❌ Employee record not found.");
        return;
      }

      const employeeData = employeeSnap.data();

      // Update the employee record with the remark
      const updatedData = {
        ...employeeData,
        adminRemark: remarkText.trim(),
        remarkUpdatedAt: new Date().toISOString(),
        remarkUpdatedBy: auth.currentUser?.email || "Unknown Admin",
        lastUpdated: serverTimestamp()
      };

      // Save back to Firestore
      await setDoc(employeeRef, updatedData);

      // Update local state
      const updatedResults = results.map(record => {
        if (record.id === employeeId) {
          return {
            ...record,
            adminRemark: remarkText.trim(),
            remarkUpdatedAt: new Date().toISOString(),
            remarkUpdatedBy: auth.currentUser?.email || "Unknown Admin"
          };
        }
        return record;
      });

      setResults(updatedResults);
      setDisplayedResults(displayedResults.map(record => {
        if (record.id === employeeId) {
          return {
            ...record,
            adminRemark: remarkText.trim(),
            remarkUpdatedAt: new Date().toISOString(),
            remarkUpdatedBy: auth.currentUser?.email || "Unknown Admin"
          };
        }
        return record;
      }));

      // Clear editing state
      setEditingRemark(null);
      setRemarkText("");

      alert("✅ Remark saved successfully!");

    } catch (error) {
      console.error("Error saving remark:", error);
      alert(`❌ Failed to save remark!\n\nError: ${error.message}`);
    }
  };

  // Archive employee function (Admin only)
  const archiveEmployee = async (employeeId, employeeData) => {
    if (userRole !== "admin") {
      alert("❌ Access Denied!\n\nOnly administrators can archive employee records.");
      return;
    }

    const confirmArchive = window.confirm(
      `🗂️ Archive Employee Record\n\n` +
      `Employee: ${employeeData.name} (${employeeData.employeeNo})\n` +
      `Department: ${employeeData.departmentName || employeeData.department}\n` +
      `Unit: ${employeeData.unitName || employeeData.unit}\n\n` +
      `This will move the employee record to archived data.\n` +
      `The record will only be accessible by administrators.\n\n` +
      `Are you sure you want to archive this employee?`
    );

    if (!confirmArchive) return;

    try {
      // Create archived record with timestamp
      const archivedData = {
        ...employeeData,
        archivedAt: serverTimestamp(),
        archivedBy: auth.currentUser?.email || "Unknown Admin",
        originalId: employeeId
      };

      // Save to archived_data collection
      const archiveRef = doc(firestoreDB, "archived_data", employeeId);
      await setDoc(archiveRef, archivedData);

      // Delete from employees collection
      const employeeRef = doc(firestoreDB, "employees", employeeId);
      await deleteDoc(employeeRef);

      // Refresh the results
      const updatedResults = displayedResults.filter(record => record.id !== employeeId);
      setDisplayedResults(updatedResults);
      setResults(results.filter(record => record.id !== employeeId));

      alert(`✅ Employee Archived Successfully!\n\n${employeeData.name} (${employeeData.employeeNo}) has been moved to archived data.`);

    } catch (error) {
      console.error("Error archiving employee:", error);
      alert(`❌ Archive Failed!\n\nError: ${error.message}\n\nPlease try again.`);
    }
  };

  if (loading) {
    return <p>Loading results...</p>;
  }





  return (









    <div style={{ padding: "20px", maxWidth: "100%", margin: "0 auto", fontFamily: "Arial, sans-serif" }}>
      <h2 style={{ textAlign: "center", marginBottom: "20px" }}>Filtered Training Records</h2>

      {/* Search Input */}
      <div style={{ marginBottom: "20px" }}>
        <input
          type="text"
          placeholder="Search by Name or Employee ID..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: "100%",
            maxWidth: "400px",
            padding: "10px",
            fontSize: "16px",
            border: "2px solid #007BFF",
            borderRadius: "25px",
            outline: "none",
            boxSizing: "border-box",
            marginBottom: "10px"
          }}
        />
        {searchTerm.trim() && (
          <p style={{
            fontSize: "14px",
            color: "#666",
            margin: "5px 0 0 0",
            fontStyle: "italic"
          }}>
            Showing {displayedResults.length} result{displayedResults.length !== 1 ? 's' : ''} for "{searchTerm}"
          </p>
        )}
      </div>

      <div style={{ display: "flex", gap: "10px", marginBottom: "20px", flexWrap: "wrap" }}>
        <div>
          <label><strong>Department:</strong></label>
          <select
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
            style={{ width: "200px", padding: "8px", borderRadius: "5px" }}
          >
            {departments.map((dept) => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>
        </div>

        <div>
          <label><strong>Unit:</strong></label>
          <select
            value={selectedUnit}
            onChange={(e) => setSelectedUnit(e.target.value)}
            style={{ width: "200px", padding: "8px", borderRadius: "5px" }}
          >
            {units.map((unit) => (
              <option key={unit.id} value={unit.id}>{unit.name}</option>
            ))}
          </select>
        </div>

        <div>
          <label><strong>Filter by Qualification Type:</strong></label>
          <select
            value={selectedQualification}
            onChange={(e) => setSelectedQualification(e.target.value)}
            style={{ width: "250px", padding: "8px", borderRadius: "5px" }}
          >
            {
              !qualificationTypes.some(q => q.id === selectedQualification) &&
              <option value={selectedQualification}>{selectedQualification}</option>
            }
            {qualificationTypes.map((qual) => (
              <option key={qual.id} value={qual.id}>{qual.name}</option>
            ))}
          </select>
        </div>

        <div>
          <label><strong>Filter by SubType:</strong></label>
          <select
            value={selectedSubType}
            onChange={(e) => setSelectedSubType(e.target.value)}
            style={{
              width: "250px",
              padding: "8px",
              borderRadius: "5px",
              backgroundColor: selectedQualification === "all" ? "#f5f5f5" : "white",
              cursor: selectedQualification === "all" ? "not-allowed" : "pointer"
            }}
            disabled={selectedQualification === "all"}
          >
            <option value="all">
              {selectedQualification === "all" ? "Select Type First" : "All SubTypes"}
            </option>
            {getAvailableSubtypes().map((subtype, index) => (
              <option key={index} value={subtype}>{subtype}</option>
            ))}
          </select>
        </div>

        {/* Course Title Filter - Only show for PRCC and Fellowship programs */}
        {(selectedSubType === "PRCC - Post-registration Certificate Course " ||
          selectedSubType === "HKANM Fellowship Program") && (
          <div>
            <label><strong>Filter by Course Title:</strong></label>
            <select
              value={selectedCourseTitle}
              onChange={(e) => setSelectedCourseTitle(e.target.value)}
              style={{
                width: "300px",
                padding: "8px",
                borderRadius: "5px",
                backgroundColor: availableCourseTitles.length === 0 ? "#f5f5f5" : "white"
              }}
              disabled={availableCourseTitles.length === 0}
            >
              <option value="all">All Course Titles</option>
              {availableCourseTitles.map((courseTitle, index) => (
                <option key={index} value={courseTitle}>{courseTitle}</option>
              ))}
            </select>
          </div>
        )}


        <button
          onClick={exportToExcel}
          style={{
            padding: "8px 16px",
            backgroundColor: "#007BFF",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            alignSelf: "center"
          }}
        >
          Export to Excel (Year Obtain only)
        </button>
        <button
          onClick={exportRawToExcel}
          style={{
            padding: "8px 16px",
            backgroundColor: "#6c757d",
            color: "white",
            border: "none",
            borderRadius: "5px",
            cursor: "pointer",
            alignSelf: "center"
          }}
        >
          Export to Excel (Raw Data)
        </button>
      


      </div>

      {/* Admin Statistics Dashboard */}
      {userRole === "admin" && (
        <div style={{
          display: "flex",
          gap: "20px",
          marginBottom: "20px",
          padding: "15px",
          backgroundColor: "#f8f9fa",
          border: "1px solid #dee2e6",
          borderRadius: "8px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
        }}>
          <div style={{
            flex: 1,
            textAlign: "center",
            padding: "10px",
            backgroundColor: "#e3f2fd",
            borderRadius: "6px",
            border: "1px solid #bbdefb"
          }}>
            <div style={{ fontSize: "24px", fontWeight: "bold", color: "#1976d2" }}>
              {staffStats.total}
            </div>
            <div style={{ fontSize: "14px", color: "#666", marginTop: "5px" }}>
              Total Staff
            </div>
          </div>

          <div style={{
            flex: 1,
            textAlign: "center",
            padding: "10px",
            backgroundColor: "#e8f5e8",
            borderRadius: "6px",
            border: "1px solid #c8e6c9"
          }}>
            <div style={{ fontSize: "24px", fontWeight: "bold", color: "#388e3c" }}>
              {staffStats.withData}
            </div>
            <div style={{ fontSize: "14px", color: "#666", marginTop: "5px" }}>
              With Qualifications
            </div>
          </div>

          <div style={{
            flex: 1,
            textAlign: "center",
            padding: "10px",
            backgroundColor: "#fff3e0",
            borderRadius: "6px",
            border: "1px solid #ffcc02"
          }}>
            <div style={{ fontSize: "24px", fontWeight: "bold", color: "#f57c00" }}>
              {staffStats.total - staffStats.withData}
            </div>
            <div style={{ fontSize: "14px", color: "#666", marginTop: "5px" }}>
              No Qualifications
            </div>
          </div>

          <div style={{
            flex: 1,
            textAlign: "center",
            padding: "10px",
            backgroundColor: "#f3e5f5",
            borderRadius: "6px",
            border: "1px solid #ce93d8"
          }}>
            <div style={{ fontSize: "24px", fontWeight: "bold", color: "#7b1fa2" }}>
              {staffStats.total > 0 ? Math.round((staffStats.withData / staffStats.total) * 100) : 0}%
            </div>
            <div style={{ fontSize: "14px", color: "#666", marginTop: "5px" }}>
              Data Completion
            </div>
          </div>
        </div>
      )}

      {displayedResults.length === 0 ? (
        <p style={{ textAlign: "center", fontSize: "18px", color: "red" }}>
          {searchTerm.trim() ? `No records found matching "${searchTerm}"` : "No records found."}
        </p>
      ) : (
        <table style={tableStyle}>
          <thead>
            <tr style={headerRowStyle}>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('employeeNo')}
                title="Click to sort by Employee Number"
              >
                Employee No{getSortIndicator('employeeNo')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('name')}
                title="Click to sort by Name"
              >
                Name{getSortIndicator('name')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('departmentName')}
                title="Click to sort by Department"
              >
                Department{getSortIndicator('departmentName')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('unitName')}
                title="Click to sort by Unit"
              >
                Unit{getSortIndicator('unitName')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('rank')}
                title="Click to sort by Rank"
              >
                Rank{getSortIndicator('rank')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('qualificationCount')}
                title="Click to sort by number of qualifications"
              >
                Qualifications{getSortIndicator('qualificationCount')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('lastUpdated')}
                title="Click to sort by Last Updated date"
              >
                Last Updated{getSortIndicator('lastUpdated')}
              </th>
              <th
                style={headerCellStyle}
                onClick={() => handleSort('freshGrad')}
                title="Click to sort by Graduation Year"
              >
                Graduated This Year{getSortIndicator('freshGrad')}
              </th>
              {userRole === "admin" && <th style={headerCellStyle}>Admin Remarks</th>}
              {userRole === "admin" && <th style={headerCellStyle}>Actions</th>}
              {/* <th style={headerCellStyle}>Year of Present Rank Appointment</th>
              <th style={headerCellStyle}>Year of RN Appointment in HA</th> */}
            </tr>
          </thead>
          <tbody>
            {displayedResults.map((record, index) => (
              <tr key={record.id} style={index % 2 === 0 ? evenRowStyle : oddRowStyle}>
                <td style={cellStyle}>{record.employeeNo || "N/A"}</td><td style={cellStyle}>{record.name || "N/A"}</td>
                <td style={cellStyle}>{record.departmentName || "N/A"}</td><td style={cellStyle}>{record.unitName || "N/A"}</td>
                <td style={cellStyle}>{record.rank || "N/A"}</td>
                <td style={cellStyle}>
                  {record.qualifications && record.qualifications.length > 0 ? (
                    <table style={subTableStyle}>
                      <thead>
                        <tr style={subHeaderRowStyle}>
                          <th style={subHeaderCellStyle}>Type</th><th style={subHeaderCellStyle}>SubType</th>
                          <th style={subHeaderCellStyle}>Course Title</th><th style={subHeaderCellStyle}>Year Obtained</th>
                        </tr>
                      </thead>
                      <tbody>
                        {record.qualifications.map((qual, idx) => (
                          <tr key={idx} style={subRowStyle}>
                            <td style={subCellStyle}>{qualificationTypes.find(q => q.id === qual.type?.toLowerCase().replace(/\s+/g, "_"))?.name || qual.type || "N/A"}</td><td style={subCellStyle}>{qual.subType || "N/A"}</td>
                            <td style={subCellStyle}>{qual.fellowshipTitle || qual.courseTitle || "N/A"}</td><td style={subCellStyle}>{qual.yearObtained || "N/A"}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : "N/A"}
                </td>
                <td style={getLastUpdatedCellStyle(record.rawLastUpdated, record.qualifications)}>
                  {record.lastUpdated || "N/A"}
                </td>
                <td style={cellStyle}>
  {record.freshGrad ? record.freshGrad : "N/A"}
</td>
                {userRole === "admin" && (
                  <td style={cellStyle}>
                    {editingRemark === record.id ? (
                      <div style={{ display: "flex", flexDirection: "column", gap: "5px" }}>
                        <textarea
                          value={remarkText}
                          onChange={(e) => setRemarkText(e.target.value)}
                          placeholder="Add admin remark for this employee..."
                          style={{
                            width: "100%",
                            minHeight: "60px",
                            padding: "5px",
                            border: "1px solid #ddd",
                            borderRadius: "3px",
                            fontSize: "12px",
                            resize: "vertical"
                          }}
                        />
                        <div style={{ display: "flex", gap: "5px" }}>
                          <button
                            onClick={saveRemark}
                            style={{
                              padding: "4px 8px",
                              backgroundColor: "#28a745",
                              color: "white",
                              border: "none",
                              borderRadius: "3px",
                              fontSize: "11px",
                              cursor: "pointer"
                            }}
                          >
                            ✅ Save
                          </button>
                          <button
                            onClick={cancelEditingRemark}
                            style={{
                              padding: "4px 8px",
                              backgroundColor: "#6c757d",
                              color: "white",
                              border: "none",
                              borderRadius: "3px",
                              fontSize: "11px",
                              cursor: "pointer"
                            }}
                          >
                            ❌ Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div style={{ display: "flex", flexDirection: "column", gap: "3px" }}>
                        <div style={{
                          fontSize: "12px",
                          color: "#666",
                          fontStyle: record.adminRemark ? "normal" : "italic",
                          minHeight: "20px",
                          padding: "5px",
                          backgroundColor: record.adminRemark ? "#f8f9fa" : "transparent",
                          border: record.adminRemark ? "1px solid #e9ecef" : "1px dashed #ddd",
                          borderRadius: "3px",
                          maxWidth: "200px",
                          wordWrap: "break-word"
                        }}>
                          {record.adminRemark || "No remarks"}
                        </div>
                        <button
                          onClick={() => startEditingRemark(record.id, record.adminRemark)}
                          style={{
                            padding: "3px 8px",
                            backgroundColor: "#007bff",
                            color: "white",
                            border: "none",
                            borderRadius: "3px",
                            fontSize: "11px",
                            cursor: "pointer"
                          }}
                          title="Edit admin remark for this employee"
                        >
                          📝 Edit Remark
                        </button>
                        {record.adminRemark && (
                          <div style={{ fontSize: "9px", color: "#999", marginTop: "2px" }}>
                            Updated by: {record.remarkUpdatedBy || "Unknown"}
                            {record.remarkUpdatedAt && (
                              <>
                                <br />
                                On: {new Date(record.remarkUpdatedAt).toLocaleString()}
                              </>
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </td>
                )}
                {userRole === "admin" && (
                  <td style={cellStyle}>
                    <button
                      onClick={() => archiveEmployee(record.id, record)}
                      style={{
                        padding: "6px 12px",
                        backgroundColor: "#dc3545",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                        fontSize: "12px",
                        fontWeight: "bold"
                      }}
                      title="Archive this employee record"
                    >
                      🗂️ Archive
                    </button>
                  </td>
                )}
                {/* <td style={cellStyle}>
                  {record.appointmentDate && typeof record.appointmentDate === "string"
                      ? record.appointmentDate
                      : "N/A"}
                </td>
                <td style={cellStyle}>
                  {record.rnAppointmentDate && typeof record.rnAppointmentDate === "string"
                      ? record.rnAppointmentDate
                      : "N/A"}
                </td> */}
              </tr>
            ))}
          </tbody>
        </table>
      )}


    </div>





  );
}

export default ViewResults;
